import sqlite3 from 'sqlite3'
import path from 'path'

// Database connection singleton
class DatabaseConnection {
  private static instance: DatabaseConnection
  private db: sqlite3.Database | null = null

  private constructor() {}

  public static getInstance(): DatabaseConnection {
    if (!DatabaseConnection.instance) {
      DatabaseConnection.instance = new DatabaseConnection()
    }
    return DatabaseConnection.instance
  }

  public async connect(): Promise<sqlite3.Database> {
    if (this.db) {
      return this.db
    }

    const dbPath = path.join(process.cwd(), 'database', 'khobra_kitchens.db')
    
    return new Promise((resolve, reject) => {
      this.db = new sqlite3.Database(dbPath, (err) => {
        if (err) {
          console.error('Error opening database:', err.message)
          reject(err)
        } else {
          console.log('✅ Connected to SQLite database')
          resolve(this.db!)
        }
      })
    })
  }

  public async close(): Promise<void> {
    if (this.db) {
      return new Promise((resolve, reject) => {
        this.db!.close((err) => {
          if (err) {
            reject(err)
          } else {
            this.db = null
            resolve()
          }
        })
      })
    }
  }

  public getDatabase(): sqlite3.Database | null {
    return this.db
  }
}

// Helper functions for database operations
export const dbGet = (query: string, params: any[] = []): Promise<any> => {
  return new Promise(async (resolve, reject) => {
    try {
      const db = await DatabaseConnection.getInstance().connect()
      db.get(query, params, (err, row) => {
        if (err) {
          reject(err)
        } else {
          resolve(row)
        }
      })
    } catch (error) {
      reject(error)
    }
  })
}

export const dbAll = (query: string, params: any[] = []): Promise<any[]> => {
  return new Promise(async (resolve, reject) => {
    try {
      const db = await DatabaseConnection.getInstance().connect()
      db.all(query, params, (err, rows) => {
        if (err) {
          reject(err)
        } else {
          resolve(rows || [])
        }
      })
    } catch (error) {
      reject(error)
    }
  })
}

export const dbRun = (query: string, params: any[] = []): Promise<sqlite3.RunResult> => {
  return new Promise(async (resolve, reject) => {
    try {
      const db = await DatabaseConnection.getInstance().connect()
      db.run(query, params, function(err) {
        if (err) {
          reject(err)
        } else {
          resolve(this)
        }
      })
    } catch (error) {
      reject(error)
    }
  })
}

export default DatabaseConnection
