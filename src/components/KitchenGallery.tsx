'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { motion } from 'framer-motion'
import { Swiper, SwiperSlide } from 'swiper/react'
import { Navigation, Pagination, Autoplay } from 'swiper/modules'
import 'swiper/css'
import 'swiper/css/navigation'
import 'swiper/css/pagination'

interface KitchenImage {
  id: number
  image_url: string
  alt_text: string
  sort_order: number
  is_primary: number
}

interface Kitchen {
  id: number
  title: string
  description: string
  category_name: string
  category_slug: string
  images: KitchenImage[]
}

const KitchenGallery = () => {
  const [kitchens, setKitchens] = useState<Kitchen[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchKitchens = async () => {
      try {
        const response = await fetch('/api/kitchens')
        if (response.ok) {
          const data = await response.json()
          // Show only featured kitchens or first 6 kitchens
          setKitchens(data.slice(0, 6))
        }
      } catch (error) {
        console.error('Error fetching kitchens:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchKitchens()
  }, [])

  if (loading) {
    return (
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-500 mx-auto"></div>
          </div>
        </div>
      </section>
    )
  }

  return (
    <section className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            معرض المطابخ
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            اكتشف مجموعتنا المتنوعة من المطابخ العصرية والكلاسيكية المصممة بأعلى معايير الجودة والحرفية
          </p>
        </motion.div>

        {/* Kitchen Cards */}
        {kitchens.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
            {kitchens.map((kitchen, index) => (
              <motion.div
                key={kitchen.id}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="group bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 overflow-hidden"
              >
                {/* Image Slider */}
                <div className="relative h-64 overflow-hidden">
                  {kitchen.images && kitchen.images.length > 0 ? (
                    <Swiper
                      modules={[Navigation, Pagination, Autoplay]}
                      navigation
                      pagination={{ clickable: true }}
                      autoplay={{ delay: 3000, disableOnInteraction: false }}
                      className="h-full"
                    >
                      {kitchen.images.map((image) => (
                        <SwiperSlide key={image.id}>
                          <img
                            src={image.image_url}
                            alt={image.alt_text || kitchen.title}
                            className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                          />
                        </SwiperSlide>
                      ))}
                    </Swiper>
                  ) : (
                    <div className="w-full h-full bg-gradient-to-br from-gray-200 to-gray-300 flex items-center justify-center">
                      <span className="text-gray-500 text-lg">لا توجد صور</span>
                    </div>
                  )}
                  
                  {/* Category Badge */}
                  {kitchen.category_name && (
                    <div className="absolute top-4 right-4 bg-primary-500 text-white px-3 py-1 rounded-full text-sm font-medium">
                      {kitchen.category_name}
                    </div>
                  )}
                </div>

                {/* Content */}
                <div className="p-6">
                  <h3 className="text-xl font-bold text-gray-900 mb-3 group-hover:text-primary-600 transition-colors">
                    {kitchen.title}
                  </h3>
                  {kitchen.description && (
                    <p className="text-gray-600 line-clamp-2 mb-4">
                      {kitchen.description}
                    </p>
                  )}
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-500">
                      {kitchen.images?.length || 0} صورة
                    </span>
                    <button className="text-primary-500 hover:text-primary-600 font-medium transition-colors">
                      عرض التفاصيل ←
                    </button>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <p className="text-gray-500 text-lg">لا توجد مطابخ متاحة حالياً</p>
          </div>
        )}

        {/* View All Button */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.3 }}
          viewport={{ once: true }}
          className="text-center"
        >
          <Link
            href="/kitchens"
            className="inline-flex items-center bg-gradient-to-r from-primary-500 to-primary-600 text-white px-8 py-4 rounded-full font-bold text-lg hover:from-primary-600 hover:to-primary-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105"
          >
            عرض جميع المطابخ
            <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
          </Link>
        </motion.div>
      </div>
    </section>
  )
}

export default KitchenGallery
