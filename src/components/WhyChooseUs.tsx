'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'

interface Feature {
  id: number
  icon: string
  title: string
  description: string
  gradient: string
  bg_gradient: string
  number: string
  subtitle: string
}

interface WhyChooseUsData {
  id: number | null
  title: string
  subtitle: string
  features: Feature[]
}

const WhyChooseUs = () => {
  const [data, setData] = useState<WhyChooseUsData | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await fetch('/api/why-choose-us')
        if (response.ok) {
          const result = await response.json()
          setData(result)
        }
      } catch (error) {
        console.error('Error fetching why choose us data:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [])

  // Default features if no data from database
  const defaultFeatures = [
    {
      id: 1,
      icon: '🏆',
      title: 'جودة عالية',
      description: 'نستخدم أفضل المواد والخامات في تنفيذ مشاريعنا',
      gradient: 'from-blue-500 to-purple-600',
      bg_gradient: 'from-blue-50 to-purple-50',
      number: '01',
      subtitle: 'Excellence'
    },
    {
      id: 2,
      icon: '⚡',
      title: 'تنفيذ سريع',
      description: 'نلتزم بالمواعيد المحددة ونسلم المشاريع في الوقت المناسب',
      gradient: 'from-green-500 to-teal-600',
      bg_gradient: 'from-green-50 to-teal-50',
      number: '02',
      subtitle: 'Speed'
    },
    {
      id: 3,
      icon: '💎',
      title: 'تصميم فريد',
      description: 'تصاميم مبتكرة وعصرية تناسب جميع الأذواق والمساحات',
      gradient: 'from-orange-500 to-red-600',
      bg_gradient: 'from-orange-50 to-red-50',
      number: '03',
      subtitle: 'Design'
    },
    {
      id: 4,
      icon: '🛠️',
      title: 'خدمة شاملة',
      description: 'من التصميم إلى التركيب مع ضمان شامل على جميع أعمالنا',
      gradient: 'from-purple-500 to-pink-600',
      bg_gradient: 'from-purple-50 to-pink-50',
      number: '04',
      subtitle: 'Service'
    }
  ]

  const features = data?.features?.length ? data.features : defaultFeatures

  if (loading) {
    return (
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-500 mx-auto"></div>
          </div>
        </div>
      </section>
    )
  }

  return (
    <section className="py-20 bg-gradient-to-br from-gray-50 to-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            {data?.title || 'لماذا تختار عجائب الخبراء؟'}
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            {data?.subtitle || 'نحن نقدم أفضل الخدمات في تصميم وتنفيذ المطابخ والخزائن'}
          </p>
        </motion.div>

        {/* Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {features.map((feature, index) => (
            <motion.div
              key={feature.id}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="group"
            >
              <div className={`relative p-8 rounded-2xl bg-gradient-to-br ${feature.bg_gradient || 'from-blue-50 to-purple-50'} hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 border border-white/50`}>
                {/* Number */}
                <div className="absolute top-4 right-4 text-6xl font-bold text-gray-200 group-hover:text-gray-300 transition-colors">
                  {feature.number || `0${index + 1}`}
                </div>

                {/* Icon */}
                <div className={`w-16 h-16 rounded-xl bg-gradient-to-br ${feature.gradient || 'from-blue-500 to-purple-600'} flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300`}>
                  <span className="text-2xl">{feature.icon}</span>
                </div>

                {/* Content */}
                <h3 className="text-xl font-bold text-gray-900 mb-3">
                  {feature.title}
                </h3>
                <p className="text-gray-600 leading-relaxed mb-4">
                  {feature.description}
                </p>
                
                {/* Subtitle */}
                {feature.subtitle && (
                  <div className="text-sm font-medium text-gray-500 uppercase tracking-wider">
                    {feature.subtitle}
                  </div>
                )}

                {/* Hover effect */}
                <div className={`absolute inset-0 rounded-2xl bg-gradient-to-br ${feature.gradient || 'from-blue-500 to-purple-600'} opacity-0 group-hover:opacity-5 transition-opacity duration-300`}></div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Bottom CTA */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.5 }}
          viewport={{ once: true }}
          className="text-center mt-16"
        >
          <div className="bg-gradient-to-r from-primary-500 to-primary-600 rounded-2xl p-8 md:p-12 text-white">
            <h3 className="text-2xl md:text-3xl font-bold mb-4">
              جاهز لبدء مشروعك؟
            </h3>
            <p className="text-lg mb-8 opacity-90">
              تواصل معنا اليوم واحصل على استشارة مجانية وعرض سعر مخصص لمشروعك
            </p>
            <button className="bg-white text-primary-600 px-8 py-4 rounded-full font-bold text-lg hover:bg-gray-100 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105">
              احصل على عرض سعر مجاني
            </button>
          </div>
        </motion.div>
      </div>
    </section>
  )
}

export default WhyChooseUs
