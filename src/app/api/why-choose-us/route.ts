import { NextRequest, NextResponse } from 'next/server'
import { dbGet, dbAll, dbRun } from '@/lib/database/connection'

// GET /api/why-choose-us - Get why choose us data
export async function GET() {
  try {
    const whyChooseUs = await dbGet(
      'SELECT * FROM why_choose_us WHERE is_active = 1 ORDER BY id DESC LIMIT 1'
    )

    if (!whyChooseUs) {
      return NextResponse.json({
        id: null,
        title: 'لماذا تختار عجائب الخبراء؟',
        subtitle: 'نحن نقدم أفضل الخدمات في تصميم وتنفيذ المطابخ والخزائن',
        features: []
      })
    }

    // Get features
    const features = await dbAll(
      'SELECT * FROM why_choose_us_features WHERE why_choose_us_id = ? AND is_active = 1 ORDER BY sort_order',
      [whyChooseUs.id]
    )

    return NextResponse.json({
      ...whyChooseUs,
      features: features || []
    })
  } catch (error) {
    console.error('Error fetching why choose us data:', error)
    return NextResponse.json(
      { error: 'Failed to fetch why choose us data' },
      { status: 500 }
    )
  }
}

// PUT /api/why-choose-us - Update why choose us data
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { title, subtitle, features } = body

    if (!title) {
      return NextResponse.json(
        { error: 'Title is required' },
        { status: 400 }
      )
    }

    // Check if why choose us section exists
    const existing = await dbGet('SELECT id FROM why_choose_us LIMIT 1')

    let whyChooseUsId: number

    if (existing) {
      // Update existing section
      await dbRun(
        'UPDATE why_choose_us SET title = ?, subtitle = ? WHERE id = ?',
        [title, subtitle, existing.id]
      )
      whyChooseUsId = existing.id
    } else {
      // Create new section
      const result = await dbRun(
        'INSERT INTO why_choose_us (title, subtitle) VALUES (?, ?)',
        [title, subtitle]
      )
      whyChooseUsId = result.lastID!
    }

    // Update features
    if (features && Array.isArray(features)) {
      // Clear existing features
      await dbRun('DELETE FROM why_choose_us_features WHERE why_choose_us_id = ?', [whyChooseUsId])
      
      // Insert new features
      for (let i = 0; i < features.length; i++) {
        const feature = features[i]
        await dbRun(
          `INSERT INTO why_choose_us_features 
           (why_choose_us_id, icon, title, description, gradient, bg_gradient, number, subtitle, sort_order)
           VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
          [
            whyChooseUsId,
            feature.icon,
            feature.title,
            feature.description,
            feature.gradient,
            feature.bg_gradient,
            feature.number,
            feature.subtitle,
            i
          ]
        )
      }
    }

    return NextResponse.json({
      message: 'Why choose us section updated successfully'
    })
  } catch (error) {
    console.error('Error updating why choose us data:', error)
    return NextResponse.json(
      { error: 'Failed to update why choose us data' },
      { status: 500 }
    )
  }
}
