import { NextRequest, NextResponse } from 'next/server'
import { dbAll, dbRun } from '@/lib/database/connection'

// GET /api/categories - Get all categories
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const type = searchParams.get('type') // 'kitchen' or 'cabinet'

    let query = 'SELECT * FROM categories WHERE is_active = 1'
    const params: any[] = []

    if (type) {
      query += ' AND type = ?'
      params.push(type)
    }

    query += ' ORDER BY name'

    const categories = await dbAll(query, params)

    return NextResponse.json(categories || [])
  } catch (error) {
    console.error('Error fetching categories:', error)
    return NextResponse.json(
      { error: 'Failed to fetch categories' },
      { status: 500 }
    )
  }
}

// POST /api/categories - Add new category
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { name, slug, description, type } = body

    if (!name || !type) {
      return NextResponse.json(
        { error: 'Name and type are required' },
        { status: 400 }
      )
    }

    if (!['kitchen', 'cabinet'].includes(type)) {
      return NextResponse.json(
        { error: 'Type must be either "kitchen" or "cabinet"' },
        { status: 400 }
      )
    }

    const result = await dbRun(
      'INSERT INTO categories (name, slug, description, type) VALUES (?, ?, ?, ?)',
      [name, slug || name.toLowerCase().replace(/\s+/g, '-'), description, type]
    )

    return NextResponse.json({
      id: result.lastID,
      name,
      slug: slug || name.toLowerCase().replace(/\s+/g, '-'),
      description,
      type,
      message: 'Category added successfully'
    })
  } catch (error) {
    console.error('Error adding category:', error)
    return NextResponse.json(
      { error: 'Failed to add category' },
      { status: 500 }
    )
  }
}
