import { NextRequest, NextResponse } from 'next/server'
import { dbAll, dbRun } from '@/lib/database/connection'

// GET /api/cabinets - Get all cabinets
export async function GET() {
  try {
    const cabinets = await dbAll(`
      SELECT c.*, cat.name as category_name, cat.slug as category_slug
      FROM cabinets c
      LEFT JOIN categories cat ON c.category_id = cat.id
      WHERE c.is_active = 1
      ORDER BY c.sort_order, c.id
    `)

    // Get images for each cabinet
    for (const cabinet of cabinets) {
      cabinet.images = await dbAll(
        'SELECT * FROM cabinet_images WHERE cabinet_id = ? ORDER BY sort_order',
        [cabinet.id]
      )
    }

    return NextResponse.json(cabinets)
  } catch (error) {
    console.error('Error fetching cabinets:', error)
    return NextResponse.json(
      { error: 'Failed to fetch cabinets' },
      { status: 500 }
    )
  }
}

// POST /api/cabinets - Add new cabinet
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { title, description, category_id, is_featured = 0, sort_order = 0 } = body

    if (!title) {
      return NextResponse.json(
        { error: 'Title is required' },
        { status: 400 }
      )
    }

    const result = await dbRun(
      `INSERT INTO cabinets (title, description, category_id, is_featured, sort_order)
       VALUES (?, ?, ?, ?, ?)`,
      [title, description, category_id, is_featured, sort_order]
    )

    return NextResponse.json({
      id: result.lastID,
      title,
      description,
      category_id,
      is_featured,
      sort_order,
      message: 'Cabinet added successfully'
    })
  } catch (error) {
    console.error('Error adding cabinet:', error)
    return NextResponse.json(
      { error: 'Failed to add cabinet' },
      { status: 500 }
    )
  }
}
