import { NextRequest, NextResponse } from 'next/server'
import { dbGet, dbRun, dbAll } from '@/lib/database/connection'

// GET /api/cabinets/[id] - Get specific cabinet
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const cabinet = await dbGet(
      `SELECT c.*, cat.name as category_name, cat.slug as category_slug
       FROM cabinets c
       LEFT JOIN categories cat ON c.category_id = cat.id
       WHERE c.id = ?`,
      [params.id]
    )

    if (!cabinet) {
      return NextResponse.json(
        { error: 'Cabinet not found' },
        { status: 404 }
      )
    }

    // Get images for the cabinet
    cabinet.images = await dbAll(
      'SELECT * FROM cabinet_images WHERE cabinet_id = ? ORDER BY sort_order',
      [params.id]
    )

    return NextResponse.json(cabinet)
  } catch (error) {
    console.error('Error fetching cabinet:', error)
    return NextResponse.json(
      { error: 'Failed to fetch cabinet' },
      { status: 500 }
    )
  }
}

// PUT /api/cabinets/[id] - Update cabinet
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json()
    const { title, description, category_id, is_featured, sort_order } = body

    if (!title) {
      return NextResponse.json(
        { error: 'Title is required' },
        { status: 400 }
      )
    }

    await dbRun(
      `UPDATE cabinets 
       SET title = ?, description = ?, category_id = ?, is_featured = ?, sort_order = ?
       WHERE id = ?`,
      [title, description, category_id, is_featured, sort_order, params.id]
    )

    return NextResponse.json({
      message: 'Cabinet updated successfully'
    })
  } catch (error) {
    console.error('Error updating cabinet:', error)
    return NextResponse.json(
      { error: 'Failed to update cabinet' },
      { status: 500 }
    )
  }
}

// DELETE /api/cabinets/[id] - Delete cabinet
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // First delete associated images
    await dbRun('DELETE FROM cabinet_images WHERE cabinet_id = ?', [params.id])
    
    // Then delete the cabinet
    await dbRun('DELETE FROM cabinets WHERE id = ?', [params.id])

    return NextResponse.json({
      message: 'Cabinet deleted successfully'
    })
  } catch (error) {
    console.error('Error deleting cabinet:', error)
    return NextResponse.json(
      { error: 'Failed to delete cabinet' },
      { status: 500 }
    )
  }
}
