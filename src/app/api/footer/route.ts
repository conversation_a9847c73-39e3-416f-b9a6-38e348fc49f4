import { NextRequest, NextResponse } from 'next/server'
import { dbAll, dbGet, dbRun } from '@/lib/database/connection'

// GET /api/footer - Get footer data
export async function GET() {
  try {
    const [socialMedia, quickLinks, contactInfo, footerSettings] = await Promise.all([
      dbAll('SELECT * FROM social_media WHERE is_active = 1 ORDER BY sort_order'),
      dbAll('SELECT * FROM quick_links WHERE is_active = 1 ORDER BY sort_order'),
      dbAll('SELECT * FROM contact_info WHERE is_active = 1 ORDER BY sort_order'),
      dbGet('SELECT * FROM footer_settings WHERE is_active = 1 ORDER BY id DESC LIMIT 1')
    ])

    return NextResponse.json({
      socialMedia: socialMedia || [],
      quickLinks: quickLinks || [],
      contactInfo: contactInfo || [],
      settings: footerSettings || { copyright_text: '© 2024 عجائب الخبراء. جميع الحقوق محفوظة.' }
    })
  } catch (error) {
    console.error('Error fetching footer data:', error)
    return NextResponse.json(
      { error: 'Failed to fetch footer data' },
      { status: 500 }
    )
  }
}

// PUT /api/footer - Update footer data
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { socialMedia, quickLinks, contactInfo, settings } = body

    // Update social media
    if (socialMedia && Array.isArray(socialMedia)) {
      // Clear existing social media
      await dbRun('DELETE FROM social_media')
      
      // Insert new social media
      for (let i = 0; i < socialMedia.length; i++) {
        const item = socialMedia[i]
        await dbRun(
          'INSERT INTO social_media (platform, url, icon, sort_order) VALUES (?, ?, ?, ?)',
          [item.platform, item.url, item.icon, i]
        )
      }
    }

    // Update quick links
    if (quickLinks && Array.isArray(quickLinks)) {
      // Clear existing quick links
      await dbRun('DELETE FROM quick_links')
      
      // Insert new quick links
      for (let i = 0; i < quickLinks.length; i++) {
        const item = quickLinks[i]
        await dbRun(
          'INSERT INTO quick_links (text, href, sort_order) VALUES (?, ?, ?)',
          [item.text, item.href, i]
        )
      }
    }

    // Update contact info
    if (contactInfo && Array.isArray(contactInfo)) {
      // Clear existing contact info
      await dbRun('DELETE FROM contact_info')
      
      // Insert new contact info
      for (let i = 0; i < contactInfo.length; i++) {
        const item = contactInfo[i]
        await dbRun(
          'INSERT INTO contact_info (icon, text, type, sort_order) VALUES (?, ?, ?, ?)',
          [item.icon, item.text, item.type || 'other', i]
        )
      }
    }

    // Update footer settings
    if (settings) {
      const existingSettings = await dbGet('SELECT id FROM footer_settings LIMIT 1')
      
      if (existingSettings) {
        await dbRun(
          'UPDATE footer_settings SET copyright_text = ? WHERE id = ?',
          [settings.copyright_text, existingSettings.id]
        )
      } else {
        await dbRun(
          'INSERT INTO footer_settings (copyright_text) VALUES (?)',
          [settings.copyright_text]
        )
      }
    }

    return NextResponse.json({
      message: 'Footer updated successfully'
    })
  } catch (error) {
    console.error('Error updating footer data:', error)
    return NextResponse.json(
      { error: 'Failed to update footer data' },
      { status: 500 }
    )
  }
}
