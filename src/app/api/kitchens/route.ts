import { NextRequest, NextResponse } from 'next/server'
import { dbAll, dbRun } from '@/lib/database/connection'

// GET /api/kitchens - Get all kitchens
export async function GET() {
  try {
    const kitchens = await dbAll(`
      SELECT k.*, c.name as category_name, c.slug as category_slug
      FROM kitchens k
      LEFT JOIN categories c ON k.category_id = c.id
      WHERE k.is_active = 1
      ORDER BY k.sort_order, k.id
    `)

    // Get images for each kitchen
    for (const kitchen of kitchens) {
      kitchen.images = await dbAll(
        'SELECT * FROM kitchen_images WHERE kitchen_id = ? ORDER BY sort_order',
        [kitchen.id]
      )
    }

    return NextResponse.json(kitchens)
  } catch (error) {
    console.error('Error fetching kitchens:', error)
    return NextResponse.json(
      { error: 'Failed to fetch kitchens' },
      { status: 500 }
    )
  }
}

// POST /api/kitchens - Add new kitchen
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { title, description, category_id, is_featured = 0, sort_order = 0 } = body

    if (!title) {
      return NextResponse.json(
        { error: 'Title is required' },
        { status: 400 }
      )
    }

    const result = await dbRun(
      `INSERT INTO kitchens (title, description, category_id, is_featured, sort_order)
       VALUES (?, ?, ?, ?, ?)`,
      [title, description, category_id, is_featured, sort_order]
    )

    return NextResponse.json({
      id: result.lastID,
      title,
      description,
      category_id,
      is_featured,
      sort_order,
      message: 'Kitchen added successfully'
    })
  } catch (error) {
    console.error('Error adding kitchen:', error)
    return NextResponse.json(
      { error: 'Failed to add kitchen' },
      { status: 500 }
    )
  }
}
