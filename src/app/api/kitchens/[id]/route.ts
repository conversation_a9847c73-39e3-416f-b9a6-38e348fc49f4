import { NextRequest, NextResponse } from 'next/server'
import { dbGet, dbRun, dbAll } from '@/lib/database/connection'

// GET /api/kitchens/[id] - Get specific kitchen
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const kitchen = await dbGet(
      `SELECT k.*, c.name as category_name, c.slug as category_slug
       FROM kitchens k
       LEFT JOIN categories c ON k.category_id = c.id
       WHERE k.id = ?`,
      [params.id]
    )

    if (!kitchen) {
      return NextResponse.json(
        { error: 'Kitchen not found' },
        { status: 404 }
      )
    }

    // Get images for the kitchen
    kitchen.images = await dbAll(
      'SELECT * FROM kitchen_images WHERE kitchen_id = ? ORDER BY sort_order',
      [params.id]
    )

    return NextResponse.json(kitchen)
  } catch (error) {
    console.error('Error fetching kitchen:', error)
    return NextResponse.json(
      { error: 'Failed to fetch kitchen' },
      { status: 500 }
    )
  }
}

// PUT /api/kitchens/[id] - Update kitchen
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json()
    const { title, description, category_id, is_featured, sort_order } = body

    if (!title) {
      return NextResponse.json(
        { error: 'Title is required' },
        { status: 400 }
      )
    }

    await dbRun(
      `UPDATE kitchens 
       SET title = ?, description = ?, category_id = ?, is_featured = ?, sort_order = ?
       WHERE id = ?`,
      [title, description, category_id, is_featured, sort_order, params.id]
    )

    return NextResponse.json({
      message: 'Kitchen updated successfully'
    })
  } catch (error) {
    console.error('Error updating kitchen:', error)
    return NextResponse.json(
      { error: 'Failed to update kitchen' },
      { status: 500 }
    )
  }
}

// DELETE /api/kitchens/[id] - Delete kitchen
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // First delete associated images
    await dbRun('DELETE FROM kitchen_images WHERE kitchen_id = ?', [params.id])
    
    // Then delete the kitchen
    await dbRun('DELETE FROM kitchens WHERE id = ?', [params.id])

    return NextResponse.json({
      message: 'Kitchen deleted successfully'
    })
  } catch (error) {
    console.error('Error deleting kitchen:', error)
    return NextResponse.json(
      { error: 'Failed to delete kitchen' },
      { status: 500 }
    )
  }
}
