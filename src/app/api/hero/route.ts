import { NextRequest, NextResponse } from 'next/server'
import { dbGet, dbRun } from '@/lib/database/connection'

// GET /api/hero - Get hero section data
export async function GET() {
  try {
    const hero = await dbGet(
      'SELECT * FROM hero_section WHERE is_active = 1 ORDER BY id DESC LIMIT 1'
    )

    if (!hero) {
      // Return default hero data if none exists
      return NextResponse.json({
        id: null,
        title: 'عجائب الخبراء',
        subtitle: 'تصميم وتنفيذ المطابخ والخزائن العصرية والكلاسيكية',
        background_image: null,
        primary_button_text: 'تصفح المطابخ',
        secondary_button_text: 'تواصل معنا',
        is_active: 1
      })
    }

    return NextResponse.json(hero)
  } catch (error) {
    console.error('Error fetching hero data:', error)
    return NextResponse.json(
      { error: 'Failed to fetch hero data' },
      { status: 500 }
    )
  }
}

// PUT /api/hero - Update hero section data
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { 
      title, 
      subtitle, 
      background_image, 
      primary_button_text, 
      secondary_button_text 
    } = body

    if (!title) {
      return NextResponse.json(
        { error: 'Title is required' },
        { status: 400 }
      )
    }

    // Check if hero section exists
    const existingHero = await dbGet('SELECT id FROM hero_section LIMIT 1')

    if (existingHero) {
      // Update existing hero section
      await dbRun(
        `UPDATE hero_section 
         SET title = ?, subtitle = ?, background_image = ?, 
             primary_button_text = ?, secondary_button_text = ?
         WHERE id = ?`,
        [title, subtitle, background_image, primary_button_text, secondary_button_text, existingHero.id]
      )
    } else {
      // Create new hero section
      await dbRun(
        `INSERT INTO hero_section (title, subtitle, background_image, primary_button_text, secondary_button_text)
         VALUES (?, ?, ?, ?, ?)`,
        [title, subtitle, background_image, primary_button_text, secondary_button_text]
      )
    }

    return NextResponse.json({
      message: 'Hero section updated successfully'
    })
  } catch (error) {
    console.error('Error updating hero data:', error)
    return NextResponse.json(
      { error: 'Failed to update hero data' },
      { status: 500 }
    )
  }
}
